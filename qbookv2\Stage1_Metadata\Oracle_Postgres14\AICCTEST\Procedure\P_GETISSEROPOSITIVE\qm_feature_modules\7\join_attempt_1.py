import re
def join(data,sch):
    # Enhance the join module to address the identified issues
    # Responsibility: select&from&where
    # Analyze the input and transform accordingly

    # Ensure proper handling of SELECT INTO STRICT syntax
    data = re.sub(r'INTO\s+(?!STRICT)(\w+)', r'INTO STRICT \1', data, flags=re.DOTALL | re.I)

    # Ensure proper handling of JOIN conditions
    data = re.sub(r'LEFT OUTER JOIN\s+(\w+)\s+ON\s+(\w+\.\w+)\s*=\s*(\w+\.\w+)', r'LEFT OUTER JOIN \1 ON \2 = \3', data, flags=re.DOTALL | re.I)

    return data